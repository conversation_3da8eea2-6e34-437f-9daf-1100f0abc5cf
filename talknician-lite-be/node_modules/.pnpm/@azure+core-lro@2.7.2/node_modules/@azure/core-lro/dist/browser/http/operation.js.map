{"version": 3, "file": "operation.js", "sourceRoot": "", "sources": ["../../../src/http/operation.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAiBlC,OAAO,EAAE,aAAa,EAAE,aAAa,EAAE,MAAM,wBAAwB,CAAC;AAEtE,OAAO,EAAE,MAAM,EAAE,MAAM,cAAc,CAAC;AAEtC,SAAS,8BAA8B,CAAC,MAGvC;IACC,MAAM,EAAE,mBAAmB,EAAE,iBAAiB,EAAE,GAAG,MAAM,CAAC;IAC1D,OAAO,iBAAiB,aAAjB,iBAAiB,cAAjB,iBAAiB,GAAI,mBAAmB,CAAC;AAClD,CAAC;AAED,SAAS,iBAAiB,CAAC,WAAwB;IACjD,OAAO,WAAW,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;AACzC,CAAC;AAED,SAAS,0BAA0B,CAAC,WAAwB;IAC1D,OAAO,WAAW,CAAC,OAAO,CAAC,oBAAoB,CAAC,CAAC;AACnD,CAAC;AAED,SAAS,4BAA4B,CAAC,WAAwB;IAC5D,OAAO,WAAW,CAAC,OAAO,CAAC,sBAAsB,CAAC,CAAC;AACrD,CAAC;AAED,SAAS,oBAAoB,CAAC,MAK7B;;IACC,MAAM,EAAE,QAAQ,EAAE,aAAa,EAAE,WAAW,EAAE,sBAAsB,EAAE,GAAG,MAAM,CAAC;IAChF,QAAQ,aAAa,EAAE,CAAC;QACtB,KAAK,KAAK,CAAC,CAAC,CAAC;YACX,OAAO,WAAW,CAAC;QACrB,CAAC;QACD,KAAK,QAAQ,CAAC,CAAC,CAAC;YACd,OAAO,SAAS,CAAC;QACnB,CAAC;QACD,KAAK,OAAO,CAAC,CAAC,CAAC;YACb,OAAO,MAAA,UAAU,EAAE,mCAAI,WAAW,CAAC;QACrC,CAAC;QACD,OAAO,CAAC,CAAC,CAAC;YACR,OAAO,UAAU,EAAE,CAAC;QACtB,CAAC;IACH,CAAC;IAED,SAAS,UAAU;QACjB,QAAQ,sBAAsB,EAAE,CAAC;YAC/B,KAAK,uBAAuB,CAAC,CAAC,CAAC;gBAC7B,OAAO,SAAS,CAAC;YACnB,CAAC;YACD,KAAK,cAAc,CAAC,CAAC,CAAC;gBACpB,OAAO,WAAW,CAAC;YACrB,CAAC;YACD,KAAK,UAAU,CAAC;YAChB,OAAO,CAAC,CAAC,CAAC;gBACR,OAAO,QAAQ,CAAC;YAClB,CAAC;QACH,CAAC;IACH,CAAC;AACH,CAAC;AAED,MAAM,UAAU,YAAY,CAAC,MAK5B;IACC,MAAM,EAAE,WAAW,EAAE,aAAa,EAAE,WAAW,EAAE,sBAAsB,EAAE,GAAG,MAAM,CAAC;IACnF,MAAM,iBAAiB,GAAG,0BAA0B,CAAC,WAAW,CAAC,CAAC;IAClE,MAAM,mBAAmB,GAAG,4BAA4B,CAAC,WAAW,CAAC,CAAC;IACtE,MAAM,UAAU,GAAG,8BAA8B,CAAC,EAAE,iBAAiB,EAAE,mBAAmB,EAAE,CAAC,CAAC;IAC9F,MAAM,QAAQ,GAAG,iBAAiB,CAAC,WAAW,CAAC,CAAC;IAChD,MAAM,uBAAuB,GAAG,aAAa,aAAb,aAAa,uBAAb,aAAa,CAAE,iBAAiB,EAAE,CAAC;IACnE,IAAI,UAAU,KAAK,SAAS,EAAE,CAAC;QAC7B,OAAO;YACL,IAAI,EAAE,mBAAmB;YACzB,iBAAiB,EAAE,UAAU;YAC7B,gBAAgB,EAAE,oBAAoB,CAAC;gBACrC,aAAa,EAAE,uBAAuB;gBACtC,QAAQ;gBACR,WAAW;gBACX,sBAAsB;aACvB,CAAC;SACH,CAAC;IACJ,CAAC;SAAM,IAAI,QAAQ,KAAK,SAAS,EAAE,CAAC;QAClC,OAAO;YACL,IAAI,EAAE,kBAAkB;YACxB,iBAAiB,EAAE,QAAQ;SAC5B,CAAC;IACJ,CAAC;SAAM,IAAI,uBAAuB,KAAK,KAAK,IAAI,WAAW,EAAE,CAAC;QAC5D,OAAO;YACL,IAAI,EAAE,MAAM;YACZ,iBAAiB,EAAE,WAAW;SAC/B,CAAC;IACJ,CAAC;SAAM,CAAC;QACN,OAAO,SAAS,CAAC;IACnB,CAAC;AACH,CAAC;AAED,SAAS,eAAe,CAAC,MAA+C;IACtE,MAAM,EAAE,MAAM,EAAE,UAAU,EAAE,GAAG,MAAM,CAAC;IACtC,IAAI,OAAO,MAAM,KAAK,QAAQ,IAAI,MAAM,KAAK,SAAS,EAAE,CAAC;QACvD,MAAM,IAAI,KAAK,CACb,oGAAoG,MAAM,sIAAsI,CACjP,CAAC;IACJ,CAAC;IACD,QAAQ,MAAM,aAAN,MAAM,uBAAN,MAAM,CAAE,iBAAiB,EAAE,EAAE,CAAC;QACpC,KAAK,SAAS;YACZ,OAAO,iBAAiB,CAAC,UAAU,CAAC,CAAC;QACvC,KAAK,WAAW;YACd,OAAO,WAAW,CAAC;QACrB,KAAK,QAAQ;YACX,OAAO,QAAQ,CAAC;QAClB,KAAK,SAAS,CAAC;QACf,KAAK,UAAU,CAAC;QAChB,KAAK,SAAS,CAAC;QACf,KAAK,WAAW,CAAC;QACjB,KAAK,YAAY;YACf,OAAO,SAAS,CAAC;QACnB,KAAK,UAAU,CAAC;QAChB,KAAK,WAAW;YACd,OAAO,UAAU,CAAC;QACpB,OAAO,CAAC,CAAC,CAAC;YACR,MAAM,CAAC,OAAO,CAAC,uCAAuC,MAAM,EAAE,CAAC,CAAC;YAChE,OAAO,MAAyB,CAAC;QACnC,CAAC;IACH,CAAC;AACH,CAAC;AAED,SAAS,SAAS,CAAC,WAAwB;;IACzC,MAAM,EAAE,MAAM,EAAE,GAAG,MAAC,WAAW,CAAC,IAAqB,mCAAI,EAAE,CAAC;IAC5D,OAAO,eAAe,CAAC,EAAE,MAAM,EAAE,UAAU,EAAE,WAAW,CAAC,UAAU,EAAE,CAAC,CAAC;AACzE,CAAC;AAED,SAAS,oBAAoB,CAAC,WAAwB;;IACpD,MAAM,EAAE,UAAU,EAAE,iBAAiB,EAAE,GAAG,MAAC,WAAW,CAAC,IAAqB,mCAAI,EAAE,CAAC;IACnF,MAAM,MAAM,GAAG,MAAA,UAAU,aAAV,UAAU,uBAAV,UAAU,CAAE,iBAAiB,mCAAI,iBAAiB,CAAC;IAClE,OAAO,eAAe,CAAC,EAAE,MAAM,EAAE,UAAU,EAAE,WAAW,CAAC,UAAU,EAAE,CAAC,CAAC;AACzE,CAAC;AAED,SAAS,iBAAiB,CAAC,UAAkB;IAC3C,IAAI,UAAU,KAAK,GAAG,EAAE,CAAC;QACvB,OAAO,SAAS,CAAC;IACnB,CAAC;SAAM,IAAI,UAAU,GAAG,GAAG,EAAE,CAAC;QAC5B,OAAO,WAAW,CAAC;IACrB,CAAC;SAAM,CAAC;QACN,OAAO,QAAQ,CAAC;IAClB,CAAC;AACH,CAAC;AAED,MAAM,UAAU,eAAe,CAAI,EAAE,WAAW,EAAkB;IAChE,MAAM,UAAU,GAAuB,WAAW,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC;IAC1E,IAAI,UAAU,KAAK,SAAS,EAAE,CAAC;QAC7B,wEAAwE;QACxE,MAAM,mBAAmB,GAAG,QAAQ,CAAC,UAAU,CAAC,CAAC;QACjD,OAAO,KAAK,CAAC,mBAAmB,CAAC;YAC/B,CAAC,CAAC,gCAAgC,CAAC,IAAI,IAAI,CAAC,UAAU,CAAC,CAAC;YACxD,CAAC,CAAC,mBAAmB,GAAG,IAAI,CAAC;IACjC,CAAC;IACD,OAAO,SAAS,CAAC;AACnB,CAAC;AAED,MAAM,UAAU,oBAAoB,CAAI,QAAwB;IAC9D,MAAM,KAAK,GAAG,kBAAkB,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;IACpD,IAAI,CAAC,KAAK,EAAE,CAAC;QACX,MAAM,CAAC,OAAO,CACZ,yFAAyF,CAC1F,CAAC;QACF,OAAO;IACT,CAAC;IACD,IAAI,CAAC,KAAK,CAAC,IAAI,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,CAAC;QAClC,MAAM,CAAC,OAAO,CACZ,iHAAiH,CAClH,CAAC;QACF,OAAO;IACT,CAAC;IACD,OAAO,KAAiB,CAAC;AAC3B,CAAC;AAED,SAAS,gCAAgC,CAAC,cAAoB;IAC5D,MAAM,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,IAAI,EAAE,CAAC,OAAO,EAAE,CAAC,CAAC;IACjD,MAAM,cAAc,GAAG,cAAc,CAAC,OAAO,EAAE,CAAC;IAChD,IAAI,OAAO,GAAG,cAAc,EAAE,CAAC;QAC7B,OAAO,cAAc,GAAG,OAAO,CAAC;IAClC,CAAC;IACD,OAAO,SAAS,CAAC;AACnB,CAAC;AAED,MAAM,UAAU,4BAA4B,CAAS,MAIpD;IACC,MAAM,EAAE,QAAQ,EAAE,KAAK,EAAE,iBAAiB,EAAE,GAAG,MAAM,CAAC;IACtD,SAAS,MAAM;;QACb,MAAM,IAAI,GAAG,MAAA,KAAK,CAAC,MAAM,CAAC,QAAQ,0CAAG,MAAM,CAAC,CAAC;QAC7C,QAAQ,IAAI,EAAE,CAAC;YACb,KAAK,SAAS;gBACZ,OAAO,iBAAiB,CAAC,QAAQ,CAAC,WAAW,CAAC,UAAU,CAAC,CAAC;YAC5D,KAAK,MAAM;gBACT,OAAO,kBAAkB,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC;YAC7C;gBACE,OAAO,SAAS,CAAC;QACrB,CAAC;IACH,CAAC;IACD,MAAM,MAAM,GAAG,MAAM,EAAE,CAAC;IACxB,OAAO,MAAM,KAAK,SAAS,IAAI,iBAAiB,KAAK,SAAS,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,MAAM,CAAC;AACxF,CAAC;AAED;;GAEG;AACH,MAAM,CAAC,KAAK,UAAU,iBAAiB,CAAkB,MAMxD;IACC,MAAM,EAAE,UAAU,EAAE,sBAAsB,EAAE,aAAa,EAAE,GAAG,EAAE,gBAAgB,EAAE,GAAG,MAAM,CAAC;IAC5F,OAAO,aAAa,CAAC;QACnB,IAAI,EAAE,KAAK,IAAI,EAAE;YACf,MAAM,QAAQ,GAAG,MAAM,GAAG,CAAC,kBAAkB,EAAE,CAAC;YAChD,MAAM,MAAM,GAAG,YAAY,CAAC;gBAC1B,WAAW,EAAE,QAAQ,CAAC,WAAW;gBACjC,WAAW,EAAE,GAAG,CAAC,WAAW;gBAC5B,aAAa,EAAE,GAAG,CAAC,aAAa;gBAChC,sBAAsB;aACvB,CAAC,CAAC;YACH,uBACE,QAAQ,EACR,iBAAiB,EAAE,MAAM,aAAN,MAAM,uBAAN,MAAM,CAAE,iBAAiB,EAC5C,gBAAgB,EAAE,MAAM,aAAN,MAAM,uBAAN,MAAM,CAAE,gBAAgB,IACvC,CAAC,CAAA,MAAM,aAAN,MAAM,uBAAN,MAAM,CAAE,IAAI,EAAC,CAAC,CAAC,EAAE,QAAQ,EAAE,EAAE,IAAI,EAAE,MAAM,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAC5D;QACJ,CAAC;QACD,UAAU;QACV,aAAa,EAAE,aAAa;YAC1B,CAAC,CAAC,CAAC,EAAE,YAAY,EAAE,EAAE,KAAK,EAAE,EAAE,CAAC,aAAa,CAAC,YAAY,EAAE,KAAK,CAAC;YACjE,CAAC,CAAC,CAAC,EAAE,YAAY,EAAE,EAAE,EAAE,CAAC,YAAuB;QACjD,kBAAkB,EAAE,4BAA4B;QAChD,gBAAgB;KACjB,CAAC,CAAC;AACL,CAAC;AAED,MAAM,UAAU,oBAAoB,CAClC,EAAE,WAAW,EAAe,EAC5B,KAAuC;;IAEvC,MAAM,IAAI,GAAG,MAAA,KAAK,CAAC,MAAM,CAAC,QAAQ,0CAAG,MAAM,CAAC,CAAC;IAC7C,QAAQ,IAAI,EAAE,CAAC;QACb,KAAK,mBAAmB,CAAC,CAAC,CAAC;YACzB,OAAO,8BAA8B,CAAC;gBACpC,iBAAiB,EAAE,0BAA0B,CAAC,WAAW,CAAC;gBAC1D,mBAAmB,EAAE,4BAA4B,CAAC,WAAW,CAAC;aAC/D,CAAC,CAAC;QACL,CAAC;QACD,KAAK,kBAAkB,CAAC,CAAC,CAAC;YACxB,OAAO,iBAAiB,CAAC,WAAW,CAAC,CAAC;QACxC,CAAC;QACD,KAAK,MAAM,CAAC;QACZ,OAAO,CAAC,CAAC,CAAC;YACR,OAAO,SAAS,CAAC;QACnB,CAAC;IACH,CAAC;AACH,CAAC;AAED,MAAM,UAAU,kBAAkB,CAChC,EAAE,WAAW,EAAe,EAC5B,KAAuC;;IAEvC,MAAM,IAAI,GAAG,MAAA,KAAK,CAAC,MAAM,CAAC,QAAQ,0CAAG,MAAM,CAAC,CAAC;IAC7C,QAAQ,IAAI,EAAE,CAAC;QACb,KAAK,mBAAmB,CAAC,CAAC,CAAC;YACzB,OAAO,SAAS,CAAC,WAAW,CAAC,CAAC;QAChC,CAAC;QACD,KAAK,kBAAkB,CAAC,CAAC,CAAC;YACxB,OAAO,iBAAiB,CAAC,WAAW,CAAC,UAAU,CAAC,CAAC;QACnD,CAAC;QACD,KAAK,MAAM,CAAC,CAAC,CAAC;YACZ,OAAO,oBAAoB,CAAC,WAAW,CAAC,CAAC;QAC3C,CAAC;QACD;YACE,MAAM,IAAI,KAAK,CAAC,8CAA8C,IAAI,EAAE,CAAC,CAAC;IAC1E,CAAC;AACH,CAAC;AAED,SAAS,kBAAkB,CACzB,EAAE,YAAY,EAAE,WAAW,EAAe,EAC1C,IAAO;;IAEP,OAAO,MAAC,YAA6B,aAA7B,YAAY,uBAAZ,YAAY,CAAoB,IAAI,CAAC,mCAAI,MAAC,WAAW,CAAC,IAAqB,0CAAG,IAAI,CAAC,CAAC;AAC9F,CAAC;AAED,MAAM,UAAU,mBAAmB,CACjC,GAAgB,EAChB,KAAuC;IAEvC,MAAM,GAAG,GAAG,kBAAkB,CAAC,GAAG,EAAE,kBAAkB,CAAC,CAAC;IACxD,IAAI,GAAG,IAAI,OAAO,GAAG,KAAK,QAAQ,EAAE,CAAC;QACnC,KAAK,CAAC,MAAM,CAAC,gBAAgB,GAAG,GAAG,CAAC;IACtC,CAAC;IACD,OAAO,KAAK,CAAC,MAAM,CAAC,gBAAgB,CAAC;AACvC,CAAC;AAED,MAAM,UAAU,gBAAgB,CAAC,CAAQ;IACvC,OAAO,CAAC,CAAC,IAAI,KAAK,WAAW,CAAC;AAChC,CAAC;AAED,wCAAwC;AACxC,MAAM,CAAC,KAAK,UAAU,iBAAiB,CAAkB,MAUxD;IACC,MAAM,EACJ,GAAG,EACH,UAAU,EACV,OAAO,EACP,aAAa,EACb,WAAW,EACX,QAAQ,EACR,KAAK,EACL,gBAAgB,GACjB,GAAG,MAAM,CAAC;IACX,OAAO,aAAa,CAAC;QACnB,KAAK;QACL,UAAU;QACV,QAAQ;QACR,aAAa,EAAE,aAAa;YAC1B,CAAC,CAAC,CAAC,EAAE,YAAY,EAAE,EAAE,UAAU,EAAE,EAAE,CAAC,aAAa,CAAC,YAAY,EAAE,UAAU,CAAC;YAC3E,CAAC,CAAC,CAAC,EAAE,YAAY,EAAE,EAAE,EAAE,CAAC,YAAuB;QACjD,QAAQ,EAAE,oBAAoB;QAC9B,WAAW;QACX,kBAAkB,EAAE,eAAe;QACnC,oBAAoB;QACpB,kBAAkB;QAClB,gBAAgB;QAChB,mBAAmB;QACnB,OAAO;QACP;;;WAGG;QACH,IAAI,EAAE,KAAK,EAAE,QAAgB,EAAE,YAAgD,EAAE,EAAE,CACjF,GAAG,CAAC,eAAe,CAAC,QAAQ,EAAE,YAAY,CAAC;QAC7C,gBAAgB;KACjB,CAAC,CAAC;AACL,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport {\n  HttpOperationMode,\n  LongRunningOperation,\n  LroResourceLocationConfig,\n  LroResponse,\n  RawResponse,\n  ResponseBody,\n} from \"./models.js\";\nimport {\n  LroError,\n  OperationConfig,\n  OperationStatus,\n  RestorableOperationState,\n  StateProxy,\n} from \"../poller/models.js\";\nimport { initOperation, pollOperation } from \"../poller/operation.js\";\nimport { AbortSignalLike } from \"@azure/abort-controller\";\nimport { logger } from \"../logger.js\";\n\nfunction getOperationLocationPollingUrl(inputs: {\n  operationLocation?: string;\n  azureAsyncOperation?: string;\n}): string | undefined {\n  const { azureAsyncOperation, operationLocation } = inputs;\n  return operationLocation ?? azureAsyncOperation;\n}\n\nfunction getLocationHeader(rawResponse: RawResponse): string | undefined {\n  return rawResponse.headers[\"location\"];\n}\n\nfunction getOperationLocationHeader(rawResponse: RawResponse): string | undefined {\n  return rawResponse.headers[\"operation-location\"];\n}\n\nfunction getAzureAsyncOperationHeader(rawResponse: RawResponse): string | undefined {\n  return rawResponse.headers[\"azure-asyncoperation\"];\n}\n\nfunction findResourceLocation(inputs: {\n  requestMethod?: string;\n  location?: string;\n  requestPath?: string;\n  resourceLocationConfig?: LroResourceLocationConfig;\n}): string | undefined {\n  const { location, requestMethod, requestPath, resourceLocationConfig } = inputs;\n  switch (requestMethod) {\n    case \"PUT\": {\n      return requestPath;\n    }\n    case \"DELETE\": {\n      return undefined;\n    }\n    case \"PATCH\": {\n      return getDefault() ?? requestPath;\n    }\n    default: {\n      return getDefault();\n    }\n  }\n\n  function getDefault() {\n    switch (resourceLocationConfig) {\n      case \"azure-async-operation\": {\n        return undefined;\n      }\n      case \"original-uri\": {\n        return requestPath;\n      }\n      case \"location\":\n      default: {\n        return location;\n      }\n    }\n  }\n}\n\nexport function inferLroMode(inputs: {\n  rawResponse: RawResponse;\n  requestPath?: string;\n  requestMethod?: string;\n  resourceLocationConfig?: LroResourceLocationConfig;\n}): (OperationConfig & { mode: HttpOperationMode }) | undefined {\n  const { rawResponse, requestMethod, requestPath, resourceLocationConfig } = inputs;\n  const operationLocation = getOperationLocationHeader(rawResponse);\n  const azureAsyncOperation = getAzureAsyncOperationHeader(rawResponse);\n  const pollingUrl = getOperationLocationPollingUrl({ operationLocation, azureAsyncOperation });\n  const location = getLocationHeader(rawResponse);\n  const normalizedRequestMethod = requestMethod?.toLocaleUpperCase();\n  if (pollingUrl !== undefined) {\n    return {\n      mode: \"OperationLocation\",\n      operationLocation: pollingUrl,\n      resourceLocation: findResourceLocation({\n        requestMethod: normalizedRequestMethod,\n        location,\n        requestPath,\n        resourceLocationConfig,\n      }),\n    };\n  } else if (location !== undefined) {\n    return {\n      mode: \"ResourceLocation\",\n      operationLocation: location,\n    };\n  } else if (normalizedRequestMethod === \"PUT\" && requestPath) {\n    return {\n      mode: \"Body\",\n      operationLocation: requestPath,\n    };\n  } else {\n    return undefined;\n  }\n}\n\nfunction transformStatus(inputs: { status: unknown; statusCode: number }): OperationStatus {\n  const { status, statusCode } = inputs;\n  if (typeof status !== \"string\" && status !== undefined) {\n    throw new Error(\n      `Polling was unsuccessful. Expected status to have a string value or no value but it has instead: ${status}. This doesn't necessarily indicate the operation has failed. Check your Azure subscription or resource status for more information.`,\n    );\n  }\n  switch (status?.toLocaleLowerCase()) {\n    case undefined:\n      return toOperationStatus(statusCode);\n    case \"succeeded\":\n      return \"succeeded\";\n    case \"failed\":\n      return \"failed\";\n    case \"running\":\n    case \"accepted\":\n    case \"started\":\n    case \"canceling\":\n    case \"cancelling\":\n      return \"running\";\n    case \"canceled\":\n    case \"cancelled\":\n      return \"canceled\";\n    default: {\n      logger.verbose(`LRO: unrecognized operation status: ${status}`);\n      return status as OperationStatus;\n    }\n  }\n}\n\nfunction getStatus(rawResponse: RawResponse): OperationStatus {\n  const { status } = (rawResponse.body as ResponseBody) ?? {};\n  return transformStatus({ status, statusCode: rawResponse.statusCode });\n}\n\nfunction getProvisioningState(rawResponse: RawResponse): OperationStatus {\n  const { properties, provisioningState } = (rawResponse.body as ResponseBody) ?? {};\n  const status = properties?.provisioningState ?? provisioningState;\n  return transformStatus({ status, statusCode: rawResponse.statusCode });\n}\n\nfunction toOperationStatus(statusCode: number): OperationStatus {\n  if (statusCode === 202) {\n    return \"running\";\n  } else if (statusCode < 300) {\n    return \"succeeded\";\n  } else {\n    return \"failed\";\n  }\n}\n\nexport function parseRetryAfter<T>({ rawResponse }: LroResponse<T>): number | undefined {\n  const retryAfter: string | undefined = rawResponse.headers[\"retry-after\"];\n  if (retryAfter !== undefined) {\n    // Retry-After header value is either in HTTP date format, or in seconds\n    const retryAfterInSeconds = parseInt(retryAfter);\n    return isNaN(retryAfterInSeconds)\n      ? calculatePollingIntervalFromDate(new Date(retryAfter))\n      : retryAfterInSeconds * 1000;\n  }\n  return undefined;\n}\n\nexport function getErrorFromResponse<T>(response: LroResponse<T>): LroError | undefined {\n  const error = accessBodyProperty(response, \"error\");\n  if (!error) {\n    logger.warning(\n      `The long-running operation failed but there is no error property in the response's body`,\n    );\n    return;\n  }\n  if (!error.code || !error.message) {\n    logger.warning(\n      `The long-running operation failed but the error property in the response's body doesn't contain code or message`,\n    );\n    return;\n  }\n  return error as LroError;\n}\n\nfunction calculatePollingIntervalFromDate(retryAfterDate: Date): number | undefined {\n  const timeNow = Math.floor(new Date().getTime());\n  const retryAfterTime = retryAfterDate.getTime();\n  if (timeNow < retryAfterTime) {\n    return retryAfterTime - timeNow;\n  }\n  return undefined;\n}\n\nexport function getStatusFromInitialResponse<TState>(inputs: {\n  response: LroResponse<unknown>;\n  state: RestorableOperationState<TState>;\n  operationLocation?: string;\n}): OperationStatus {\n  const { response, state, operationLocation } = inputs;\n  function helper(): OperationStatus {\n    const mode = state.config.metadata?.[\"mode\"];\n    switch (mode) {\n      case undefined:\n        return toOperationStatus(response.rawResponse.statusCode);\n      case \"Body\":\n        return getOperationStatus(response, state);\n      default:\n        return \"running\";\n    }\n  }\n  const status = helper();\n  return status === \"running\" && operationLocation === undefined ? \"succeeded\" : status;\n}\n\n/**\n * Initiates the long-running operation.\n */\nexport async function initHttpOperation<TResult, TState>(inputs: {\n  stateProxy: StateProxy<TState, TResult>;\n  resourceLocationConfig?: LroResourceLocationConfig;\n  processResult?: (result: unknown, state: TState) => TResult;\n  setErrorAsResult: boolean;\n  lro: LongRunningOperation;\n}): Promise<RestorableOperationState<TState>> {\n  const { stateProxy, resourceLocationConfig, processResult, lro, setErrorAsResult } = inputs;\n  return initOperation({\n    init: async () => {\n      const response = await lro.sendInitialRequest();\n      const config = inferLroMode({\n        rawResponse: response.rawResponse,\n        requestPath: lro.requestPath,\n        requestMethod: lro.requestMethod,\n        resourceLocationConfig,\n      });\n      return {\n        response,\n        operationLocation: config?.operationLocation,\n        resourceLocation: config?.resourceLocation,\n        ...(config?.mode ? { metadata: { mode: config.mode } } : {}),\n      };\n    },\n    stateProxy,\n    processResult: processResult\n      ? ({ flatResponse }, state) => processResult(flatResponse, state)\n      : ({ flatResponse }) => flatResponse as TResult,\n    getOperationStatus: getStatusFromInitialResponse,\n    setErrorAsResult,\n  });\n}\n\nexport function getOperationLocation<TState>(\n  { rawResponse }: LroResponse,\n  state: RestorableOperationState<TState>,\n): string | undefined {\n  const mode = state.config.metadata?.[\"mode\"];\n  switch (mode) {\n    case \"OperationLocation\": {\n      return getOperationLocationPollingUrl({\n        operationLocation: getOperationLocationHeader(rawResponse),\n        azureAsyncOperation: getAzureAsyncOperationHeader(rawResponse),\n      });\n    }\n    case \"ResourceLocation\": {\n      return getLocationHeader(rawResponse);\n    }\n    case \"Body\":\n    default: {\n      return undefined;\n    }\n  }\n}\n\nexport function getOperationStatus<TState>(\n  { rawResponse }: LroResponse,\n  state: RestorableOperationState<TState>,\n): OperationStatus {\n  const mode = state.config.metadata?.[\"mode\"];\n  switch (mode) {\n    case \"OperationLocation\": {\n      return getStatus(rawResponse);\n    }\n    case \"ResourceLocation\": {\n      return toOperationStatus(rawResponse.statusCode);\n    }\n    case \"Body\": {\n      return getProvisioningState(rawResponse);\n    }\n    default:\n      throw new Error(`Internal error: Unexpected operation mode: ${mode}`);\n  }\n}\n\nfunction accessBodyProperty<P extends string>(\n  { flatResponse, rawResponse }: LroResponse,\n  prop: P,\n): ResponseBody[P] {\n  return (flatResponse as ResponseBody)?.[prop] ?? (rawResponse.body as ResponseBody)?.[prop];\n}\n\nexport function getResourceLocation<TState>(\n  res: LroResponse,\n  state: RestorableOperationState<TState>,\n): string | undefined {\n  const loc = accessBodyProperty(res, \"resourceLocation\");\n  if (loc && typeof loc === \"string\") {\n    state.config.resourceLocation = loc;\n  }\n  return state.config.resourceLocation;\n}\n\nexport function isOperationError(e: Error): boolean {\n  return e.name === \"RestError\";\n}\n\n/** Polls the long-running operation. */\nexport async function pollHttpOperation<TState, TResult>(inputs: {\n  lro: LongRunningOperation;\n  stateProxy: StateProxy<TState, TResult>;\n  processResult?: (result: unknown, state: TState) => TResult;\n  updateState?: (state: TState, lastResponse: LroResponse) => void;\n  isDone?: (lastResponse: LroResponse, state: TState) => boolean;\n  setDelay: (intervalInMs: number) => void;\n  options?: { abortSignal?: AbortSignalLike };\n  state: RestorableOperationState<TState>;\n  setErrorAsResult: boolean;\n}): Promise<void> {\n  const {\n    lro,\n    stateProxy,\n    options,\n    processResult,\n    updateState,\n    setDelay,\n    state,\n    setErrorAsResult,\n  } = inputs;\n  return pollOperation({\n    state,\n    stateProxy,\n    setDelay,\n    processResult: processResult\n      ? ({ flatResponse }, inputState) => processResult(flatResponse, inputState)\n      : ({ flatResponse }) => flatResponse as TResult,\n    getError: getErrorFromResponse,\n    updateState,\n    getPollingInterval: parseRetryAfter,\n    getOperationLocation,\n    getOperationStatus,\n    isOperationError,\n    getResourceLocation,\n    options,\n    /**\n     * The expansion here is intentional because `lro` could be an object that\n     * references an inner this, so we need to preserve a reference to it.\n     */\n    poll: async (location: string, inputOptions?: { abortSignal?: AbortSignalLike }) =>\n      lro.sendPollRequest(location, inputOptions),\n    setErrorAsResult,\n  });\n}\n"]}