{"version": 3, "file": "poller.js", "sourceRoot": "", "sources": ["../../../src/http/poller.ts"], "names": [], "mappings": ";AAAA,uCAAuC;AACvC,kCAAkC;;;AAIlC,iDASwB;AAExB,mDAAwD;AAExD;;;;;GAKG;AACI,KAAK,UAAU,gBAAgB,CACpC,GAAyB,EACzB,OAAkD;IAElD,MAAM,EACJ,sBAAsB,EACtB,YAAY,EACZ,aAAa,EACb,WAAW,EACX,WAAW,EACX,qBAAqB,EACrB,qBAAqB,GAAG,KAAK,GAC9B,GAAG,OAAO,IAAI,EAAE,CAAC;IAClB,OAAO,IAAA,6BAAiB,EAA+B;QACrD,4BAA4B,EAA5B,2CAA4B;QAC5B,yBAAyB,EAAE,iCAAkB;QAC7C,gBAAgB,EAAhB,+BAAgB;QAChB,oBAAoB,EAApB,mCAAoB;QACpB,mBAAmB,EAAnB,kCAAmB;QACnB,kBAAkB,EAAE,8BAAe;QACnC,QAAQ,EAAE,mCAAoB;QAC9B,qBAAqB;KACtB,CAAC,CACA;QACE,IAAI,EAAE,KAAK,IAAI,EAAE;YACf,MAAM,QAAQ,GAAG,MAAM,GAAG,CAAC,kBAAkB,EAAE,CAAC;YAChD,MAAM,MAAM,GAAG,IAAA,2BAAY,EAAC;gBAC1B,WAAW,EAAE,QAAQ,CAAC,WAAW;gBACjC,WAAW,EAAE,GAAG,CAAC,WAAW;gBAC5B,aAAa,EAAE,GAAG,CAAC,aAAa;gBAChC,sBAAsB;aACvB,CAAC,CAAC;YACH,uBACE,QAAQ,EACR,iBAAiB,EAAE,MAAM,aAAN,MAAM,uBAAN,MAAM,CAAE,iBAAiB,EAC5C,gBAAgB,EAAE,MAAM,aAAN,MAAM,uBAAN,MAAM,CAAE,gBAAgB,IACvC,CAAC,CAAA,MAAM,aAAN,MAAM,uBAAN,MAAM,CAAE,IAAI,EAAC,CAAC,CAAC,EAAE,QAAQ,EAAE,EAAE,IAAI,EAAE,MAAM,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAC5D;QACJ,CAAC;QACD,IAAI,EAAE,GAAG,CAAC,eAAe;KAC1B,EACD;QACE,YAAY;QACZ,qBAAqB;QACrB,WAAW;QACX,WAAW;QACX,aAAa,EAAE,aAAa;YAC1B,CAAC,CAAC,CAAC,EAAE,YAAY,EAAE,EAAE,KAAK,EAAE,EAAE,CAAC,aAAa,CAAC,YAAY,EAAE,KAAK,CAAC;YACjE,CAAC,CAAC,CAAC,EAAE,YAAY,EAAE,EAAE,EAAE,CAAC,YAAuB;KAClD,CACF,CAAC;AACJ,CAAC;AAnDD,4CAmDC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport { LongRunningOperation, LroResponse } from \"./models.js\";\nimport { OperationState, SimplePollerLike } from \"../poller/models.js\";\nimport {\n  getErrorFromResponse,\n  getOperationLocation,\n  getOperationStatus,\n  getResourceLocation,\n  getStatusFromInitialResponse,\n  inferLroMode,\n  isOperationError,\n  parseRetryAfter,\n} from \"./operation.js\";\nimport { CreateHttpPollerOptions } from \"./models.js\";\nimport { buildCreatePoller } from \"../poller/poller.js\";\n\n/**\n * Creates a poller that can be used to poll a long-running operation.\n * @param lro - Description of the long-running operation\n * @param options - options to configure the poller\n * @returns an initialized poller\n */\nexport async function createHttpPoller<TResult, TState extends OperationState<TResult>>(\n  lro: LongRunningOperation,\n  options?: CreateHttpPollerOptions<TResult, TState>,\n): Promise<SimplePollerLike<TState, TResult>> {\n  const {\n    resourceLocationConfig,\n    intervalInMs,\n    processResult,\n    restoreFrom,\n    updateState,\n    withOperationLocation,\n    resolveOnUnsuccessful = false,\n  } = options || {};\n  return buildCreatePoller<LroResponse, TResult, TState>({\n    getStatusFromInitialResponse,\n    getStatusFromPollResponse: getOperationStatus,\n    isOperationError,\n    getOperationLocation,\n    getResourceLocation,\n    getPollingInterval: parseRetryAfter,\n    getError: getErrorFromResponse,\n    resolveOnUnsuccessful,\n  })(\n    {\n      init: async () => {\n        const response = await lro.sendInitialRequest();\n        const config = inferLroMode({\n          rawResponse: response.rawResponse,\n          requestPath: lro.requestPath,\n          requestMethod: lro.requestMethod,\n          resourceLocationConfig,\n        });\n        return {\n          response,\n          operationLocation: config?.operationLocation,\n          resourceLocation: config?.resourceLocation,\n          ...(config?.mode ? { metadata: { mode: config.mode } } : {}),\n        };\n      },\n      poll: lro.sendPollRequest,\n    },\n    {\n      intervalInMs,\n      withOperationLocation,\n      restoreFrom,\n      updateState,\n      processResult: processResult\n        ? ({ flatResponse }, state) => processResult(flatResponse, state)\n        : ({ flatResponse }) => flatResponse as TResult,\n    },\n  );\n}\n"]}