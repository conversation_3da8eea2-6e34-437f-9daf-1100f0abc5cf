{"version": 3, "file": "httpClientAdapter.js", "sourceRoot": "", "sources": ["../../src/httpClientAdapter.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAIlC,OAAO,EAAE,kBAAkB,EAAE,MAAM,eAAe,CAAC;AACnD,OAAO,EAAE,iBAAiB,EAAE,MAAM,WAAW,CAAC;AAE9C;;;;GAIG;AACH,MAAM,UAAU,iBAAiB,CAAC,mBAAkC;IAClE,OAAO;QACL,WAAW,EAAE,KAAK,EAAE,OAAwB,EAA6B,EAAE;YACzE,MAAM,QAAQ,GAAG,MAAM,mBAAmB,CAAC,WAAW,CACpD,iBAAiB,CAAC,OAAO,EAAE,EAAE,WAAW,EAAE,IAAI,EAAE,CAAC,CAClD,CAAC;YACF,OAAO,kBAAkB,CAAC,QAAQ,CAAC,CAAC;QACtC,CAAC;KACF,CAAC;AACJ,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport type { HttpClient, PipelineRequest, PipelineResponse } from \"@azure/core-rest-pipeline\";\nimport type { RequestPolicy } from \"./policies/requestPolicyFactoryPolicy.js\";\nimport { toPipelineResponse } from \"./response.js\";\nimport { toWebResourceLike } from \"./util.js\";\n\n/**\n * Converts a RequestPolicy based HttpClient to a PipelineRequest based HttpClient.\n * @param requestPolicyClient - A HttpClient compatible with core-http\n * @returns A HttpClient compatible with core-rest-pipeline\n */\nexport function convertHttpClient(requestPolicyClient: RequestPolicy): HttpClient {\n  return {\n    sendRequest: async (request: PipelineRequest): Promise<PipelineResponse> => {\n      const response = await requestPolicyClient.sendRequest(\n        toWebResourceLike(request, { createProxy: true }),\n      );\n      return toPipelineResponse(response);\n    },\n  };\n}\n"]}