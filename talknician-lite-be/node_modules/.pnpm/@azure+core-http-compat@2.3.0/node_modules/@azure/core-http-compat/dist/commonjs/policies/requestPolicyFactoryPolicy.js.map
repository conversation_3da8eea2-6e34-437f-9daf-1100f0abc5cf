{"version": 3, "file": "requestPolicyFactoryPolicy.js", "sourceRoot": "", "sources": ["../../../src/policies/requestPolicyFactoryPolicy.ts"], "names": [], "mappings": ";AAAA,uCAAuC;AACvC,kCAAkC;;;AA+DlC,4EAuBC;AA7ED,wCAAkE;AAElE,gDAAsE;AAStE;;GAEG;AACH,IAAY,oBAKX;AALD,WAAY,oBAAoB;IAC9B,iEAAS,CAAA;IACT,+DAAQ,CAAA;IACR,6DAAO,CAAA;IACP,qEAAW,CAAA;AACb,CAAC,EALW,oBAAoB,oCAApB,oBAAoB,QAK/B;AAUD,MAAM,wBAAwB,GAA6B;IACzD,GAAG,CAAC,SAA+B,EAAE,QAAgB;QACnD,gBAAgB;IAClB,CAAC;IACD,SAAS,CAAC,SAA+B;QACvC,OAAO,KAAK,CAAC;IACf,CAAC;CACF,CAAC;AASF;;GAEG;AACU,QAAA,8BAA8B,GAAG,4BAA4B,CAAC;AAE3E;;;GAGG;AACH,SAAgB,gCAAgC,CAC9C,SAAiC;IAEjC,MAAM,gBAAgB,GAAG,SAAS,CAAC,KAAK,EAAE,CAAC,OAAO,EAAE,CAAC;IAErD,OAAO;QACL,IAAI,EAAE,sCAA8B;QACpC,KAAK,CAAC,WAAW,CAAC,OAAwB,EAAE,IAAiB;YAC3D,IAAI,YAAY,GAAkB;gBAChC,KAAK,CAAC,WAAW,CAAC,WAAW;oBAC3B,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,IAAA,2BAAiB,EAAC,WAAW,CAAC,CAAC,CAAC;oBAC5D,OAAO,IAAA,8BAAgB,EAAC,QAAQ,EAAE,EAAE,WAAW,EAAE,IAAI,EAAE,CAAC,CAAC;gBAC3D,CAAC;aACF,CAAC;YACF,KAAK,MAAM,OAAO,IAAI,gBAAgB,EAAE,CAAC;gBACvC,YAAY,GAAG,OAAO,CAAC,MAAM,CAAC,YAAY,EAAE,wBAAwB,CAAC,CAAC;YACxE,CAAC;YAED,MAAM,eAAe,GAAG,IAAA,2BAAiB,EAAC,OAAO,EAAE,EAAE,WAAW,EAAE,IAAI,EAAE,CAAC,CAAC;YAC1E,MAAM,QAAQ,GAAG,MAAM,YAAY,CAAC,WAAW,CAAC,eAAe,CAAC,CAAC;YACjE,OAAO,IAAA,gCAAkB,EAAC,QAAQ,CAAC,CAAC;QACtC,CAAC;KACF,CAAC;AACJ,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport type {\n  PipelinePolicy,\n  PipelineRequest,\n  PipelineResponse,\n  SendRequest,\n} from \"@azure/core-rest-pipeline\";\nimport type { WebResourceLike } from \"../util.js\";\nimport { toPipelineRequest, toWebResourceLike } from \"../util.js\";\nimport type { CompatResponse } from \"../response.js\";\nimport { toCompatResponse, toPipelineResponse } from \"../response.js\";\n\n/**\n * A compatible interface for core-http request policies\n */\nexport interface RequestPolicy {\n  sendRequest(httpRequest: WebResourceLike): Promise<CompatResponse>;\n}\n\n/**\n * An enum for compatibility with RequestPolicy\n */\nexport enum HttpPipelineLogLevel {\n  ERROR = 1,\n  INFO = 3,\n  OFF = 0,\n  WARNING = 2,\n}\n\n/**\n * An interface for compatibility with RequestPolicy\n */\nexport interface RequestPolicyOptionsLike {\n  log(logLevel: HttpPipelineLogLevel, message: string): void;\n  shouldLog(logLevel: HttpPipelineLogLevel): boolean;\n}\n\nconst mockRequestPolicyOptions: RequestPolicyOptionsLike = {\n  log(_logLevel: HttpPipelineLogLevel, _message: string): void {\n    /* do nothing */\n  },\n  shouldLog(_logLevel: HttpPipelineLogLevel): boolean {\n    return false;\n  },\n};\n\n/**\n * An interface for compatibility with core-http's RequestPolicyFactory\n */\nexport interface RequestPolicyFactory {\n  create(nextPolicy: RequestPolicy, options: RequestPolicyOptionsLike): RequestPolicy;\n}\n\n/**\n * The name of the RequestPolicyFactoryPolicy\n */\nexport const requestPolicyFactoryPolicyName = \"RequestPolicyFactoryPolicy\";\n\n/**\n * A policy that wraps policies written for core-http.\n * @param factories - An array of `RequestPolicyFactory` objects from a core-http pipeline\n */\nexport function createRequestPolicyFactoryPolicy(\n  factories: RequestPolicyFactory[],\n): PipelinePolicy {\n  const orderedFactories = factories.slice().reverse();\n\n  return {\n    name: requestPolicyFactoryPolicyName,\n    async sendRequest(request: PipelineRequest, next: SendRequest): Promise<PipelineResponse> {\n      let httpPipeline: RequestPolicy = {\n        async sendRequest(httpRequest) {\n          const response = await next(toPipelineRequest(httpRequest));\n          return toCompatResponse(response, { createProxy: true });\n        },\n      };\n      for (const factory of orderedFactories) {\n        httpPipeline = factory.create(httpPipeline, mockRequestPolicyOptions);\n      }\n\n      const webResourceLike = toWebResourceLike(request, { createProxy: true });\n      const response = await httpPipeline.sendRequest(webResourceLike);\n      return toPipelineResponse(response);\n    },\n  };\n}\n"]}