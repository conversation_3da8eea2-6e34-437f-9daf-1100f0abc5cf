{"version": 3, "file": "state-cjs.cjs", "sourceRoot": "", "sources": ["../../src/state-cjs.cts"], "names": [], "mappings": ";AAAA,uCAAuC;AACvC,kCAAkC;;;AAElC;;GAEG;AACU,QAAA,KAAK,GAAG;IACnB,mBAAmB,EAAE,IAAI,OAAO,EAAE;CACnC,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\n/**\n * Holds the singleton operationRequestMap, to be shared across CJS and ESM imports.\n */\nexport const state = {\n  operationRequestMap: new WeakMap(),\n};\n"]}