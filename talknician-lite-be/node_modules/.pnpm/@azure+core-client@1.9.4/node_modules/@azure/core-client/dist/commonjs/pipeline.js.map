{"version": 3, "file": "pipeline.js", "sourceRoot": "", "sources": ["../../src/pipeline.ts"], "names": [], "mappings": ";AAAA,uCAAuC;AACvC,kCAAkC;;AAuClC,oDAiBC;AArDD,yEAAmE;AAEnE,kEAGmC;AAEnC,qEAA+D;AAuB/D;;;;;GAKG;AACH,SAAgB,oBAAoB,CAAC,UAAyC,EAAE;IAC9E,MAAM,QAAQ,GAAG,IAAA,8CAAyB,EAAC,OAAO,aAAP,OAAO,cAAP,OAAO,GAAI,EAAE,CAAC,CAAC;IAC1D,IAAI,OAAO,CAAC,iBAAiB,EAAE,CAAC;QAC9B,QAAQ,CAAC,SAAS,CAChB,IAAA,oDAA+B,EAAC;YAC9B,UAAU,EAAE,OAAO,CAAC,iBAAiB,CAAC,UAAU;YAChD,MAAM,EAAE,OAAO,CAAC,iBAAiB,CAAC,gBAAgB;SACnD,CAAC,CACH,CAAC;IACJ,CAAC;IAED,QAAQ,CAAC,SAAS,CAAC,IAAA,4CAAmB,EAAC,OAAO,CAAC,oBAAoB,CAAC,EAAE,EAAE,KAAK,EAAE,WAAW,EAAE,CAAC,CAAC;IAC9F,QAAQ,CAAC,SAAS,CAAC,IAAA,gDAAqB,EAAC,OAAO,CAAC,sBAAsB,CAAC,EAAE;QACxE,KAAK,EAAE,aAAa;KACrB,CAAC,CAAC;IAEH,OAAO,QAAQ,CAAC;AAClB,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport type { DeserializationPolicyOptions } from \"./deserializationPolicy.js\";\nimport { deserializationPolicy } from \"./deserializationPolicy.js\";\nimport type { InternalPipelineOptions, Pipeline } from \"@azure/core-rest-pipeline\";\nimport {\n  bearerTokenAuthenticationPolicy,\n  createPipelineFromOptions,\n} from \"@azure/core-rest-pipeline\";\nimport type { SerializationPolicyOptions } from \"./serializationPolicy.js\";\nimport { serializationPolicy } from \"./serializationPolicy.js\";\nimport type { TokenCredential } from \"@azure/core-auth\";\n\n/**\n * Options for creating a Pipeline to use with ServiceClient.\n * Mostly for customizing the auth policy (if using token auth) or\n * the deserialization options when using XML.\n */\nexport interface InternalClientPipelineOptions extends InternalPipelineOptions {\n  /**\n   * Options to customize bearerTokenAuthenticationPolicy.\n   */\n  credentialOptions?: { credentialScopes: string | string[]; credential: TokenCredential };\n  /**\n   * Options to customize deserializationPolicy.\n   */\n  deserializationOptions?: DeserializationPolicyOptions;\n  /**\n   * Options to customize serializationPolicy.\n   */\n  serializationOptions?: SerializationPolicyOptions;\n}\n\n/**\n * Creates a new Pipeline for use with a Service Client.\n * Adds in deserializationPolicy by default.\n * Also adds in bearerTokenAuthenticationPolicy if passed a TokenCredential.\n * @param options - Options to customize the created pipeline.\n */\nexport function createClientPipeline(options: InternalClientPipelineOptions = {}): Pipeline {\n  const pipeline = createPipelineFromOptions(options ?? {});\n  if (options.credentialOptions) {\n    pipeline.addPolicy(\n      bearerTokenAuthenticationPolicy({\n        credential: options.credentialOptions.credential,\n        scopes: options.credentialOptions.credentialScopes,\n      }),\n    );\n  }\n\n  pipeline.addPolicy(serializationPolicy(options.serializationOptions), { phase: \"Serialize\" });\n  pipeline.addPolicy(deserializationPolicy(options.deserializationOptions), {\n    phase: \"Deserialize\",\n  });\n\n  return pipeline;\n}\n"]}