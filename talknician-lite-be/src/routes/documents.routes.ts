import express, { Request, Response } from "express";
import multer from "multer";
import { authenticate } from "../middleware/auth.middleware";
import { logger } from "../utils/logger";
import { prisma } from "../config/database";
import OpenAI from "openai";
import path from "path";
import fs from "fs";
import { azureStorageService } from "../services/azure-storage.service";

const router: express.Router = express.Router();

// Apply auth middleware to all routes
router.use(authenticate);

// Configure multer for file uploads (using memory storage for Azure upload)
const upload = multer({
  storage: multer.memoryStorage(), // Store files in memory for direct Azure upload
  limits: {
    fileSize: 50 * 1024 * 1024, // 50MB limit
  },
  fileFilter: (req, file, cb) => {
    // Allow common document types
    const allowedTypes = [
      ".pdf",
      ".docx",
      ".doc",
      ".txt",
      ".md",
      ".csv",
      ".xlsx",
      ".xls",
      ".pptx",
      ".ppt",
    ];
    const ext = path.extname(file.originalname).toLowerCase();
    if (allowedTypes.includes(ext)) {
      cb(null, true);
    } else {
      cb(new Error("File type not supported"));
    }
  },
});

// Initialize OpenAI client
const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
});

/**
 * GET /api/documents
 * Get all documents for an organization
 */
router.get("/", async (req: Request, res: Response) => {
  try {
    const { organizationId } = req.query;
    const userId = req.user?.id;

    if (!organizationId) {
      return res.status(400).json({
        success: false,
        message: "Organization ID is required",
      });
    }

    // Verify user has access to this organization
    const userOrg = req.user?.organizations.find(
      (org) => org.id === organizationId
    );
    if (!userOrg) {
      return res.status(403).json({
        success: false,
        message: "Access denied to this organization",
      });
    }

    const documents = await prisma.document.findMany({
      where: {
        organizationId: organizationId as string,
        isDeleted: false,
      },
      orderBy: { createdAt: "desc" },
    });

    res.json({
      success: true,
      data: documents.map((doc) => ({
        id: doc.id,
        filename: doc.filename,
        originalName: doc.originalName,
        size: doc.size,
        status: doc.status,
        organizationId: doc.organizationId,
        createdAt: doc.createdAt.toISOString(),
        openaiFileId: doc.openaiFileId,
        inRAG: !!doc.openaiFileId,
      })),
    });
  } catch (error) {
    logger.error("Error fetching documents:", error);
    res.status(500).json({
      success: false,
      message: "Failed to fetch documents",
    });
  }
});

/**
 * POST /api/documents/upload
 * Upload a document to Azure Storage
 */
router.post(
  "/upload",
  (req, res, next) => {
    logger.info("Upload route hit", {
      contentType: req.get("Content-Type"),
      method: req.method,
      body: req.body,
      headers: req.headers,
    });
    next();
  },
  upload.single("file"),
  async (req: Request, res: Response) => {
    try {
      const { organizationId } = req.body;
      const userId = req.user?.id;
      const file = req.file;

      if (!file) {
        return res.status(400).json({
          success: false,
          message: "No file uploaded",
        });
      }

      if (!organizationId) {
        return res.status(400).json({
          success: false,
          message: "Organization ID is required",
        });
      }

      // Verify user has access to this organization
      const userOrg = req.user?.organizations.find(
        (org) => org.id === organizationId
      );
      if (!userOrg) {
        return res.status(403).json({
          success: false,
          message: "Access denied to this organization",
        });
      }

      // Create a temporary file from memory buffer for Azure upload
      const tempDir = "temp";
      if (!fs.existsSync(tempDir)) {
        fs.mkdirSync(tempDir, { recursive: true });
      }

      const tempFilename = `${Date.now()}-${Math.round(
        Math.random() * 1e9
      )}${path.extname(file.originalname)}`;
      const tempPath = path.join(tempDir, tempFilename);

      // Write buffer to temporary file
      fs.writeFileSync(tempPath, file.buffer);

      // Create a file object compatible with Azure upload
      const tempFile = {
        ...file,
        path: tempPath,
      } as Express.Multer.File;

      // Upload to Azure Blob Storage
      const azureBlobUrl = await azureStorageService.uploadFile(
        tempFile,
        organizationId
      );

      // Create document record
      const document = await prisma.document.create({
        data: {
          filename: tempFilename, // Use the generated filename
          originalName: file.originalname,
          size: file.size,
          mimeType: file.mimetype,
          localPath: null, // No local path since we're using Azure
          azureBlobUrl, // Azure URL from upload
          organizationId,
          userId: userId!,
          uploadedBy: userId!,
          status: "COMPLETED", // File uploaded successfully to Azure Storage
        },
      });

      logger.info("Document uploaded to Azure Storage", {
        documentId: document.id,
        organizationId,
        filename: document.originalName,
        azureBlobUrl,
      });

      res.status(201).json({
        success: true,
        data: {
          id: document.id,
          filename: document.filename,
          originalName: document.originalName,
          size: document.size,
          status: document.status,
          organizationId: document.organizationId,
          createdAt: document.createdAt.toISOString(),
          openaiFileId: null, // Not in RAG yet
          inRAG: false, // Not in RAG yet
        },
      });
    } catch (error) {
      logger.error("Error uploading document:", error);
      res.status(500).json({
        success: false,
        message: "Failed to upload document",
      });
    }
  }
);

/**
 * POST /api/documents/:id/add-to-rag
 * Add an existing document to OpenAI for RAG
 */
router.post("/:id/add-to-rag", async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const userId = req.user?.id;

    const document = await prisma.document.findUnique({
      where: { id },
    });

    if (!document) {
      return res.status(404).json({
        success: false,
        message: "Document not found",
      });
    }

    // Verify user has access to this organization
    const userOrg = req.user?.organizations.find(
      (org) => org.id === document.organizationId
    );
    if (!userOrg) {
      return res.status(403).json({
        success: false,
        message: "Access denied to this organization",
      });
    }

    if (document.openaiFileId) {
      return res.status(400).json({
        success: false,
        message: "Document is already in RAG",
      });
    }

    // Check if file exists in Azure Storage
    if (!document.azureBlobUrl) {
      return res.status(400).json({
        success: false,
        message: "Document file not found in Azure Storage",
      });
    }

    // Verify file exists in Azure Storage
    const fileExists = await azureStorageService.fileExists(
      document.azureBlobUrl
    );
    if (!fileExists) {
      return res.status(400).json({
        success: false,
        message: "Document file not found in Azure Storage",
      });
    }

    try {
      // Get or create organization-specific vector store
      const vectorStoreId = await getOrCreateOrgVectorStore(
        document.organizationId
      );

      // Get file stream from Azure Storage
      const fileStream = await azureStorageService.getFileStream(
        document.azureBlobUrl
      );

      // Upload file to OpenAI
      const openaiFile = await openai.files.create({
        file: fileStream as any, // Type assertion for Azure stream compatibility
        purpose: "assistants",
      });

      // Add file to organization's vector store with metadata
      await openai.vectorStores.files.create(vectorStoreId, {
        file_id: openaiFile.id,
        attributes: {
          organizationId: document.organizationId,
          documentId: document.id,
          filename: document.filename,
          uploadedBy: document.uploadedBy,
        },
      });

      // Update document with OpenAI file ID
      const updatedDocument = await prisma.document.update({
        where: { id: document.id },
        data: {
          openaiFileId: openaiFile.id,
          status: "COMPLETED",
        },
      });

      logger.info("Document added to RAG", {
        documentId: document.id,
        openaiFileId: openaiFile.id,
        organizationId: document.organizationId,
        vectorStoreId,
      });

      res.json({
        success: true,
        data: {
          id: updatedDocument.id,
          filename: updatedDocument.filename,
          originalName: updatedDocument.originalName,
          size: updatedDocument.size,
          status: updatedDocument.status,
          organizationId: updatedDocument.organizationId,
          createdAt: updatedDocument.createdAt.toISOString(),
          openaiFileId: updatedDocument.openaiFileId,
          inRAG: true,
        },
      });
    } catch (openaiError) {
      logger.error("Error adding document to RAG:", openaiError);
      res.status(500).json({
        success: false,
        message: "Failed to add document to RAG",
      });
    }
  } catch (error) {
    logger.error("Error in add-to-rag:", error);
    res.status(500).json({
      success: false,
      message: "Internal server error",
    });
  }
});

/**
 * POST /api/documents/:id/remove-from-rag
 * Remove a document from OpenAI RAG
 */
router.post("/:id/remove-from-rag", async (req: Request, res: Response) => {
  try {
    const { id } = req.params;

    const document = await prisma.document.findUnique({
      where: { id },
    });

    if (!document) {
      return res.status(404).json({
        success: false,
        message: "Document not found",
      });
    }

    // Verify user has access to this organization
    const userOrg = req.user?.organizations.find(
      (org) => org.id === document.organizationId
    );
    if (!userOrg) {
      return res.status(403).json({
        success: false,
        message: "Access denied to this organization",
      });
    }

    if (!document.openaiFileId) {
      return res.status(400).json({
        success: false,
        message: "Document is not in RAG",
      });
    }

    try {
      // Get organization vector store
      const vectorStoreId = await getOrCreateOrgVectorStore(
        document.organizationId
      );

      // Remove file from vector store
      await openai.vectorStores.files.del(vectorStoreId, document.openaiFileId);

      // Delete the file from OpenAI
      await openai.files.del(document.openaiFileId);

      // Update document to remove OpenAI file ID
      const updatedDocument = await prisma.document.update({
        where: { id: document.id },
        data: {
          openaiFileId: null,
        },
      });

      logger.info("Document removed from RAG", {
        documentId: document.id,
        organizationId: document.organizationId,
        vectorStoreId,
      });

      res.json({
        success: true,
        data: {
          id: updatedDocument.id,
          filename: updatedDocument.filename,
          originalName: updatedDocument.originalName,
          size: updatedDocument.size,
          status: updatedDocument.status,
          organizationId: updatedDocument.organizationId,
          createdAt: updatedDocument.createdAt.toISOString(),
          openaiFileId: null,
          inRAG: false,
        },
      });
    } catch (openaiError) {
      logger.error("Error removing document from RAG:", openaiError);
      res.status(500).json({
        success: false,
        message: "Failed to remove document from RAG",
      });
    }
  } catch (error) {
    logger.error("Error in remove-from-rag:", error);
    res.status(500).json({
      success: false,
      message: "Internal server error",
    });
  }
});

/**
 * DELETE /api/documents/:id
 * Delete a document
 */
router.delete("/:id", async (req: Request, res: Response) => {
  try {
    const { id } = req.params;

    const document = await prisma.document.findUnique({
      where: { id },
    });

    if (!document) {
      return res.status(404).json({
        success: false,
        message: "Document not found",
      });
    }

    // Verify user has access to this organization
    const userOrg = req.user?.organizations.find(
      (org) => org.id === document.organizationId
    );
    if (!userOrg) {
      return res.status(403).json({
        success: false,
        message: "Access denied to this organization",
      });
    }

    // Remove from OpenAI if exists
    if (document.openaiFileId) {
      try {
        await openai.files.del(document.openaiFileId);
      } catch (openaiError) {
        logger.warn("Error removing file from OpenAI:", openaiError);
        // Continue with deletion even if OpenAI cleanup fails
      }
    }

    // Delete file from Azure Storage
    try {
      if (document.azureBlobUrl) {
        await azureStorageService.deleteFile(document.azureBlobUrl);
      }
    } catch (fileError) {
      logger.warn("Error deleting file from Azure Storage:", fileError);
      // Continue with database deletion
    }

    // Soft delete in database
    await prisma.document.update({
      where: { id },
      data: { isDeleted: true },
    });

    logger.info("Document deleted", {
      documentId: id,
      organizationId: document.organizationId,
    });

    res.json({
      success: true,
      message: "Document deleted successfully",
    });
  } catch (error) {
    logger.error("Error deleting document:", error);
    res.status(500).json({
      success: false,
      message: "Failed to delete document",
    });
  }
});

/**
 * GET /api/documents/:id/download
 * Download a document file (local only for now)
 */
router.get("/:id/download", async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const document = await prisma.document.findUnique({ where: { id } });
    if (!document) {
      return res
        .status(404)
        .json({ success: false, message: "Document not found" });
    }
    // Verify user has access to this organization
    const userOrg = req.user?.organizations.find(
      (org) => org.id === document.organizationId
    );
    if (!userOrg) {
      return res.status(403).json({
        success: false,
        message: "Access denied to this organization",
      });
    }
    // Download from Azure Storage
    if (document.azureBlobUrl) {
      try {
        res.setHeader(
          "Content-Disposition",
          `attachment; filename=\"${encodeURIComponent(
            document.originalName
          )}\"`
        );
        res.setHeader(
          "Content-Type",
          document.mimeType || "application/octet-stream"
        );

        const fileStream = await azureStorageService.getFileStream(
          document.azureBlobUrl
        );
        return fileStream.pipe(res);
      } catch (downloadError) {
        logger.error("Error downloading from Azure Storage:", downloadError);
        return res.status(500).json({
          success: false,
          message: "Failed to download file from Azure Storage",
        });
      }
    } else {
      return res
        .status(404)
        .json({ success: false, message: "File not found in Azure Storage" });
    }
  } catch (error) {
    logger.error("Error downloading document:", error);
    res
      .status(500)
      .json({ success: false, message: "Failed to download document" });
  }
});

/**
 * PATCH /api/documents/:id
 * Rename a document (update originalName)
 */
router.patch("/:id", async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const { originalName } = req.body;
    if (!originalName || typeof originalName !== "string") {
      return res
        .status(400)
        .json({ success: false, message: "originalName is required" });
    }
    const document = await prisma.document.findUnique({ where: { id } });
    if (!document) {
      return res
        .status(404)
        .json({ success: false, message: "Document not found" });
    }
    // Verify user has access to this organization
    const userOrg = req.user?.organizations.find(
      (org) => org.id === document.organizationId
    );
    if (!userOrg) {
      return res.status(403).json({
        success: false,
        message: "Access denied to this organization",
      });
    }
    const updated = await prisma.document.update({
      where: { id },
      data: { originalName },
    });
    res.json({
      success: true,
      data: { id: updated.id, originalName: updated.originalName },
    });
  } catch (error) {
    logger.error("Error renaming document:", error);
    res
      .status(500)
      .json({ success: false, message: "Failed to rename document" });
  }
});

/**
 * Helper function to get or create organization-specific vector store
 */
async function getOrCreateOrgVectorStore(
  organizationId: string
): Promise<string> {
  try {
    // Check if organization already has a vector store
    const organization = await prisma.organization.findUnique({
      where: { id: organizationId },
      select: { vectorStoreId: true, name: true },
    });

    if (!organization) {
      throw new Error("Organization not found");
    }

    // Return existing vector store if available
    if (organization.vectorStoreId) {
      return organization.vectorStoreId;
    }

    // Create new vector store for organization
    const vectorStore = await openai.vectorStores.create({
      name: `${organization.name} Knowledge Base`,
      expires_after: {
        anchor: "last_active_at",
        days: 365, // Keep for 1 year after last activity
      },
    });

    // Save vector store ID to organization
    await prisma.organization.update({
      where: { id: organizationId },
      data: { vectorStoreId: vectorStore.id },
    });

    logger.info("Created vector store for organization", {
      organizationId,
      vectorStoreId: vectorStore.id,
      organizationName: organization.name,
    });

    return vectorStore.id;
  } catch (error) {
    logger.error("Error creating vector store:", error);
    throw error;
  }
}

export default router;
